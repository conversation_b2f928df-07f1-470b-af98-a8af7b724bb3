{% extends 'base.html' %}

{% block title %}Mark Attendance - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }
    
    .form-header {
        background: var(--gradient-bg);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .quick-actions {
        background: #f8fafc;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .quick-action-btn {
        background: white;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        padding: 20px;
        border-radius: 15px;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
        height: 120px;
        cursor: pointer;
        position: relative;
    }

    .quick-action-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,123,255,0.2);
    }

    .quick-action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .quick-action-btn.success {
        border-color: #28a745;
        background-color: #d4edda;
        color: #155724;
    }

    .quick-action-btn.disabled {
        border-color: #6c757d;
        background-color: #f8f9fa;
        color: #6c757d;
        cursor: not-allowed;
    }

    .btn-spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.9);
        padding: 10px;
        border-radius: 50%;
    }
    
    .current-time {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .time-display {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .date-display {
        font-size: 1.2rem;
        opacity: 0.9;
    }
    
    .status-info {
        background: #eff6ff;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--primary-color);
    }

    .current-status {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        margin-bottom: 2rem;
    }

    .status-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        backdrop-filter: blur(10px);
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .status-item label {
        font-weight: 600;
        margin-bottom: 0;
    }

    .status-value {
        font-weight: 500;
        font-size: 1.1em;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.present {
        background-color: #28a745;
        color: white;
    }

    .status-badge.absent {
        background-color: #dc3545;
        color: white;
    }

    .status-badge.not-marked {
        background-color: #6c757d;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-clock me-3"></i>Mark Attendance
                        </h1>
                        <p class="lead mb-0">Record your daily attendance</p>
                    </div>
                    <div>
                        <a href="{% url 'core:attendance_list' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Attendance
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                
                <!-- Current Time Display -->
                <div class="current-time">
                    <div class="time-display" id="currentTime">--:--:--</div>
                    <div class="date-display" id="currentDate">Loading...</div>
                </div>
                
                <!-- Current Status -->
                <div class="current-status mb-4" id="currentStatus">
                    <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i>Today's Status</h5>
                    <div class="status-card" id="statusCard">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="status-item">
                                    <label>Check-in:</label>
                                    <span id="currentCheckIn" class="status-value">Not marked</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="status-item">
                                    <label>Check-out:</label>
                                    <span id="currentCheckOut" class="status-value">Not marked</span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <div class="status-item">
                                    <label>Status:</label>
                                    <span id="currentAttendanceStatus" class="status-badge">Not marked</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="status-item">
                                    <label>Working Hours:</label>
                                    <span id="currentWorkingHours" class="status-value">0.0 hrs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="quick-action-btn" id="checkInBtn" onclick="performQuickCheckIn()">
                                <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                                <div><strong>Check In</strong></div>
                                <small>Mark your arrival</small>
                                <div class="btn-spinner" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="quick-action-btn" id="checkOutBtn" onclick="performQuickCheckOut()">
                                <i class="fas fa-sign-out-alt fa-2x mb-2"></i>
                                <div><strong>Check Out</strong></div>
                                <small>Mark your departure</small>
                                <div class="btn-spinner" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i>
                                </div>
                            </button>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Use quick actions for instant check-in/out or use the form below for manual entry
                        </small>
                    </div>
                </div>
                
                <!-- Today's Status -->
                <div class="status-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Today's Status</h6>
                    <p class="mb-1"><strong>Status:</strong> <span class="badge bg-success">Present</span></p>
                    <p class="mb-1"><strong>Check-in:</strong> 09:15 AM</p>
                    <p class="mb-1"><strong>Working Hours:</strong> <span id="workingHours">0.0</span> hours</p>
                    <p class="mb-0"><strong>Location:</strong> Office</p>
                </div>
                
                <!-- Manual Attendance Form -->
                <form method="post" id="attendanceForm">
                    {% csrf_token %}
                    
                    <h5 class="mb-3"><i class="fas fa-edit me-2"></i>Manual Entry</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date <span class="text-danger">*</span></label>
                                <input type="date" name="date" class="form-control" value="{{ today }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status <span class="text-danger">*</span></label>
                                <select name="status" class="form-control" required>
                                    <option value="">Select Status</option>
                                    <option value="Present">Present</option>
                                    <option value="Absent">Absent</option>
                                    <option value="Leave">Leave</option>
                                    <option value="Half-Day">Half-Day</option>
                                    <option value="WFH">Work From Home</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Check-in Time</label>
                                <input type="time" name="check_in" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Check-out Time</label>
                                <input type="time" name="check_out" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Break Time (hours)</label>
                                <input type="number" name="break_time" class="form-control" step="0.25" min="0" placeholder="e.g., 1.0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Location</label>
                                <input type="text" name="location" class="form-control" placeholder="Office, Home, Client site, etc.">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Remarks</label>
                        <textarea name="remarks" class="form-control" rows="3" placeholder="Any additional notes..."></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-save me-2"></i>Save Attendance
                        </button>
                        <a href="{% url 'core:attendance_list' %}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-list me-2"></i>View Records
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load current attendance status on page load
    loadAttendanceStatus();

    // Update current time
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        document.getElementById('currentTime').textContent = timeString;
        document.getElementById('currentDate').textContent = dateString;

        // Update working hours if checked in
        updateWorkingHours();
    }
    
    function updateWorkingHours() {
        // This would calculate working hours based on check-in time
        // For demo purposes, showing a sample calculation
        const checkInTime = '09:15';
        if (checkInTime) {
            const now = new Date();
            const checkIn = new Date();
            const [hours, minutes] = checkInTime.split(':');
            checkIn.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            
            const diffMs = now - checkIn;
            const diffHours = diffMs / (1000 * 60 * 60);
            
            if (diffHours > 0) {
                document.getElementById('workingHours').textContent = diffHours.toFixed(1);
            }
        }
    }
    
    // Update time every second
    updateTime();
    setInterval(updateTime, 1000);

    // Refresh attendance status every 30 seconds
    setInterval(loadAttendanceStatus, 30000);
    
    // Load current attendance status
    function loadAttendanceStatus() {
        fetch('{% url "core:get_attendance_status" %}')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatusDisplay(data);
                    updateButtonStates(data);
                }
            })
            .catch(error => {
                console.error('Error loading attendance status:', error);
            });
    }

    // Update status display
    function updateStatusDisplay(data) {
        document.getElementById('currentCheckIn').textContent = data.check_in || 'Not marked';
        document.getElementById('currentCheckOut').textContent = data.check_out || 'Not marked';
        document.getElementById('currentWorkingHours').textContent = data.working_hours + ' hrs';

        const statusElement = document.getElementById('currentAttendanceStatus');
        statusElement.textContent = data.status;
        statusElement.className = 'status-badge ' + (data.status === 'Present' ? 'present' :
                                                     data.status === 'Absent' ? 'absent' : 'not-marked');
    }

    // Update button states
    function updateButtonStates(data) {
        const checkInBtn = document.getElementById('checkInBtn');
        const checkOutBtn = document.getElementById('checkOutBtn');

        if (data.check_in && !data.check_out) {
            // Checked in, not checked out
            checkInBtn.classList.add('success');
            checkInBtn.disabled = true;
            checkOutBtn.disabled = false;
        } else if (data.check_in && data.check_out) {
            // Both checked in and out
            checkInBtn.classList.add('success');
            checkOutBtn.classList.add('success');
            checkInBtn.disabled = true;
            checkOutBtn.disabled = true;
        } else {
            // Not checked in
            checkInBtn.disabled = false;
            checkOutBtn.disabled = true;
        }
    }

    // Quick check-in function
    window.performQuickCheckIn = function() {
        const checkInBtn = document.getElementById('checkInBtn');
        const spinner = checkInBtn.querySelector('.btn-spinner');

        // Show loading state
        checkInBtn.disabled = true;
        spinner.style.display = 'block';

        fetch('{% url "core:quick_check_in" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            spinner.style.display = 'none';

            if (data.success) {
                // Update form fields
                document.querySelector('input[name="check_in"]').value = data.check_in_time;
                document.querySelector('select[name="status"]').value = data.status;
                document.querySelector('input[name="location"]').value = data.location;

                // Reload status
                loadAttendanceStatus();

                // Show success message
                showNotification('success', data.message);
            } else {
                checkInBtn.disabled = false;
                showNotification('error', data.error);
            }
        })
        .catch(error => {
            spinner.style.display = 'none';
            checkInBtn.disabled = false;
            showNotification('error', 'Network error occurred');
            console.error('Error:', error);
        });
    };

    // Quick check-out function
    window.performQuickCheckOut = function() {
        const checkOutBtn = document.getElementById('checkOutBtn');
        const spinner = checkOutBtn.querySelector('.btn-spinner');

        // Show loading state
        checkOutBtn.disabled = true;
        spinner.style.display = 'block';

        fetch('{% url "core:quick_check_out" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            spinner.style.display = 'none';

            if (data.success) {
                // Update form fields
                document.querySelector('input[name="check_out"]').value = data.check_out_time;

                // Reload status
                loadAttendanceStatus();

                // Show success message
                showNotification('success', data.message + ' Working hours: ' + data.working_hours + ' hrs');
            } else {
                checkOutBtn.disabled = false;
                showNotification('error', data.error);
            }
        })
        .catch(error => {
            spinner.style.display = 'none';
            checkOutBtn.disabled = false;
            showNotification('error', 'Network error occurred');
            console.error('Error:', error);
        });
    };

    // Show notification
    function showNotification(type, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
    
    // Form validation
    document.getElementById('attendanceForm').addEventListener('submit', function(e) {
        const status = document.querySelector('select[name="status"]').value;
        const checkIn = document.querySelector('input[name="check_in"]').value;
        const checkOut = document.querySelector('input[name="check_out"]').value;
        
        if (status === 'Present' && !checkIn) {
            e.preventDefault();
            alert('Check-in time is required for Present status.');
            return;
        }
        
        if (checkIn && checkOut && checkOut <= checkIn) {
            e.preventDefault();
            alert('Check-out time must be after check-in time.');
            return;
        }
    });
});
</script>
{% endblock %}
